<div [class.no-pointer-events]="isLocked">
  <svg #svgElement class="interactive-diagram-svg-element"></svg>
</div>
<div *ngIf="isShowMessage" (click)="hideOnPageMessage()" class="dg-int-on-page-message">
  <tra-md [slug]="onPageMessage" [isCondensed]="true" [isFlex]="true"></tra-md>
</div>

<!-- voiceover -->
<div *ngFor="let data of voiceoverDataList">
  <render-audio
    [url]="data.url" 
    [trigger]="data.trigger" 
    [isTriggerDisabled]="!isVoiceoverEnabled()"
  ></render-audio>
</div>