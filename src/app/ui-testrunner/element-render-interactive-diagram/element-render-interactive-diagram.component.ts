import { AfterViewInit, Component, Input, OnInit, ViewChild, ElementRef, OnChanges, ChangeDetectionStrategy, ChangeDetectorRef, SimpleChanges } from '@angular/core';
import { QuestionState } from '../models';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { StyleprofileService } from 'src/app/core/styleprofile.service';
import { AuthScopeSettingsService } from 'src/app/ui-item-maker/auth-scope-settings.service'
import { LangService } from 'src/app/core/lang.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { TextToSpeechService } from '../text-to-speech.service';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from './model';
import { DgIntCallbacks, renderDgIntConstruction } from './renderer'
import { isEqual, cloneDeep, throttle } from 'lodash';
import { DgIntImageManager } from './image-manager';
import type { DgInt } from "src/assets/lib/diagramatics/config";
import { IDgIntElementDnDFreeform } from './constructions/dnd-freeform';
import { SharedObjectMapService } from './shared-object-map.service'
import { IDgIntElementMcqHotspot } from './constructions/mcq-hotspot';
import { getStyleParam, styleProfilesMap } from './style-profile';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { DgIntConstructionType } from './common';
import { PubSubTypes } from '../element-render-frame/pubsub/types';
import { Subject, Subscription } from 'rxjs';
import { HyperlinkService } from '../hyperlink.service';
import { DgIntVoiceoverData } from './constructions/dnd-common';
import { DgBookmarkData } from './constructions/common';
import { McqRenderer } from '../element-render-mcq/element-render-mcq-functions';

@Component({
  selector: 'element-render-interactive-diagram',
  templateUrl: './element-render-interactive-diagram.component.html',
  styleUrls: ['./element-render-interactive-diagram.component.scss'],
})
export class ElementRenderInteractiveDiagramComponent implements OnInit, AfterViewInit, OnChanges {

  @Input() element:IContentElementInteractiveDiagram;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() questionState:QuestionState;
  @Input() changeCounter:number;
  @Input() questionPubSub?: QuestionPubSub;
  @Input() frameWorkTags:{slug:string}[];
  
  @ViewChild('svgElement') svgElementRef: ElementRef;

  // private oldElement:IContentElementInteractiveDiagram;
  private prevConstructionType: DgIntConstructionType | undefined;
  private dgIntElement: DgInt | undefined = undefined;
  private imageManager = new DgIntImageManager();
  
  frameWorkTagsRef = new Map();
  isShowMessage: boolean = false;
  onPageMessage: string = "";
  styleProfileChangeSub: Subscription;
  otherStyles: {[key: string]: any} = {};
  voiceoverDataList: {url: string, trigger: Subject<boolean>}[] = [];
  
  constructor(
    private lang:LangService,
    private styleProfileService:StyleprofileService,
    private authScope: AuthScopeSettingsService,
    private sanitizer: DomSanitizer,
    private profile: StyleprofileService,
    public textToSpeech: TextToSpeechService,
    public whiteLabel: WhitelabelService,
    private sharedObjectMap: SharedObjectMapService,
    private hyperLinkService: HyperlinkService,
  ) { }

  ngOnInit() {
    // make sure that the answer is not shown
    delete this.element?.constructionElement?._isShowAnswer;
    delete (this.element?.constructionElement as IDgIntElementDnDFreeform)?._hoveredTargetIndex;
    delete (this.element?.constructionElement as IDgIntElementMcqHotspot)?._hoveredIndex;
    this.prevConstructionType = this.element?.constructionElement?.constructionType;
    this.initFrameworkTags();
    this.ensureInitialState();
    this.styleProfileChangeSub = this.styleProfileService.getStyleProfileChanges().subscribe((hasStyleProfile) => {
      if (hasStyleProfile) {
        const mathFontFamily = this.styleProfileService.getMathDefaultFontFamily();
        this.otherStyles['math_font_family'] = mathFontFamily;
        this.renderConstructionThrottled();
      }
    });
  }

  ngAfterViewInit(): void {
    this.renderConstructionThrottled();
  }
  
  ngOnDestroy(): void {
    if (this.dgIntElement) this.dgIntElement.removeRegisteredEventListener();
    if(this.styleProfileChangeSub) {
      this.styleProfileChangeSub.unsubscribe();
    }
  }
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes.changeCounter) {
      const sharedObject = this.sharedObjectMap.get(this.element.constructionElement) as IDgIntSharedObject;
      if (this.prevConstructionType != undefined && 
        this.element.constructionElement.constructionType != this.prevConstructionType
      ) {
        this.resetQuestionState();
      }
      if (sharedObject?._resetState) {
        delete sharedObject._resetState
        this.resetQuestionState();
      }
      this.prevConstructionType = this.element.constructionElement.constructionType;
      this.renderConstructionThrottled(sharedObject);
    }
  }
  
  resetQuestionState(){
    if (this.questionState[this.element.entryId]){
      delete this.questionState[this.element.entryId];
    }
  }
  ensureInitialState(){
    // the inner logic of the stateManager will set the initial state, but that can take a while
    // before the initial state is set by the stateManager, the testrunner will assume that there is no question here
    // and set the `isFilled` indicator to true.
    // to prevent this, we set the general `diagram-interactive` initial state here.
    if (this.questionState && this.questionState[this.element.entryId] == undefined){
      const initialState = {
        type: 'diagram-interactive',
        isCorrect: false,
        isStarted: false,
        isFilled: false,
        isResponded: false,
      }
      this.questionState[this.element.entryId] = initialState;
      if(this.questionPubSub){
        this.questionPubSub.allPub({entryId: this.element.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
      }
    }
  }
  
  // debounced
  renderConstructionThrottled = throttle((sharedObject?: IDgIntSharedObject) => {
    this.renderConstruction(sharedObject);
  }, 150);
  
  renderConstruction(sharedObject?: IDgIntSharedObject) : void {
    if (this.dgIntElement) this.dgIntElement.removeRegisteredEventListener();
    if (!this.element || !this.svgElementRef) return;
    
    const svgElement = this.svgElementRef.nativeElement;
    if (!svgElement) return;
    svgElement.innerHTML = '';
    if (this.element.constructionElement){
      this.sharedObjectMap.update(this.element.constructionElement, <IDgIntSharedObject>{ svgElement });
    }
    
    const callbacks: DgIntCallbacks = {
      showMessage: (msg: string) => { this.showOnPageMessage(msg) },
      registerVoiceoverData: (voiceoverDataList: DgIntVoiceoverData[]) => { this.registerVoiceoverData(voiceoverDataList) },
      openBookmarkLink: (bookmarkData: DgBookmarkData) => { this.openBookmarkLink(bookmarkData) },
    }
    renderDgIntConstruction(
      this.element, this.imageManager, svgElement, this.questionState, sharedObject, this.questionPubSub, this.otherStyles,
      callbacks
    ).then((int) => {
      // make sure to remove the previous event listeners
      if (this.dgIntElement) this.dgIntElement.removeRegisteredEventListener();
      // this int is needed to clear the event listeners when the component is destroyed later
      this.dgIntElement = int;
    });
  }
  
  getPresetSlug(slug: string){
    switch (slug){
      case 'mcq_max_msg': return this.getMcqMaxMsgSlug(this.frameWorkTagsRef);
      default: return slug;
    }
  }
  showOnPageMessage(msg: string){
    if (msg.startsWith('slug:')){
      let slug = msg.split(':')[1];
      msg = this.getPresetSlug(slug);
    }
    this.onPageMessage = msg;
    this.isShowMessage = Boolean(msg);
  }
  hideOnPageMessage(){
    this.isShowMessage = false;
  }
  
  initFrameworkTags(){
    if (this.frameWorkTags){
      this.frameWorkTags.forEach(tag => {
        const key = (''+tag.slug).trim();
        this.frameWorkTagsRef.set(key, true);
      })
    }
  }
  getMcqMaxMsgSlug(frameWorkTagsRef:Map<string, boolean>) {
    return McqRenderer.getMaxMcqMsgSlug(this.profile, this.whiteLabel);
  }
  openBookmarkLink(data: DgBookmarkData){
    this.hyperLinkService.linkRequest.next({
      readerElementId: undefined,
      bookmarkId: data.bookmarkId,
      itemLabel: data.targetItemLabel
    })
  }
  registerVoiceoverData(voiceoverDataList: {url: string, trigger: Subject<boolean>}[]) {
    this.voiceoverDataList = voiceoverDataList;
  }
  
  isVoiceoverEnabled(){
    return this.textToSpeech.isActive;
  }
  
}
