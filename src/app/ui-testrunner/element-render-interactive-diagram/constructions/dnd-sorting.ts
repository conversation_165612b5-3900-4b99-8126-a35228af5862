import { IDgIntConstructionElement, DgIntConstructionType, IDgIntStateManager, IDgIntDnDState } from "../common"
import { LAYOUT_HOME_POSITION, DND_UNUSED_ID, TAG_DND_BLOCK, generateDnDBlock, TAG_DND_DRAGGABLE, TAG_DND_TARGET, IDgIntHomeConfig, HOME_CONFIG_LAYOUT, resolveDnDHomeConfig, groupByHome, generateHomeTargetDiagramsWithLabel, IDgIntDnDConfig, unusedId, setupInteractiveDnD, IDgIntElementDnDAnswerSet, DnDStateManager, isConfigIndividualHome, appendHomeSuffix, HOME_LABEL_POSITION, attachLabelDiagram, IDgIntStyleConfig, DND_STYLE_MODE, resolveDnDStyleConfig, generateElementColorsObject, generatePaddedDiagram, DND_ISFILLED_MODE, generateHomeGroupBg, DgIntVoiceoverData } from "./dnd-common";
import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { formattedStringToBBCode, multilineTextWithSize, IdGenerator, IDgIntStyle, getStyle, generateImageTextDiagram, IDgIntScoringParam, flattenArray, EX_TO_EM, getFilenameFromPath } from "./common";
import { DgIntImageManager } from "../image-manager";
import { DgIntStyleParam, DgStyleProfile, getStyleParam } from "../style-profile";
import { IContentElementImage } from "../../element-render-image/model";
import { cloneDeep } from "lodash";
import { tex2dgMathExpression } from "../mathjax";
import { mathExpressionDg } from "../math-expression";
import { Subject } from "rxjs";
import { DgIntCallbacks } from "../renderer";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;
const ARROW_BASE_LENGTH = 0.8
const ARROW_HEAD_LENGTH = 0.4
const ARROW_HEAD_ANGLE = 45
const ARROW_STROKE_WIDTH = 3
const KEY_OPTION_DATA_LIST = 'dnd_sorting_option_data_list';

export enum FLOW_LAYOUT {
  LEFT_TO_RIGHT = 'left-to-right',
  RIGHT_TO_LEFT = 'right-to-left',
  TOP_TO_BOTTOM = 'top-to-bottom',
  BOTTOM_TO_TOP = 'bottom-to-top',
  // SNAKE = 'snake',
}

export enum FLOW_SYMBOL {
  NONE = 'none',
  ARROW = 'arrow',
  LINE = 'line',
}

export enum SORTING_PROPORTIONAL_SCORING_MODE {
  LONGEST_COMMON_SUBSEQUENCE = 'longest_common_subsequence',
  POSITIONAL = 'positional',
}

export interface IDgIntElementDnDSortingOption {
  label : string;
  isMath? : boolean;
  id? : string;
  targetId? : string;
  image? : IContentElementImage;
  _cachedImageSize? : {width: number, height: number};
  isPreFilled? : boolean;
  isExtra?: boolean;
  targetLabel?: string;
  isTargetLabelMath?: boolean;
  voiceover?: {url?: string, script?: string};
}
export interface IDgIntElementDnDSorting extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_SORTING;
  options: IDgIntElementDnDSortingOption[];
  extraOptions?: IDgIntElementDnDSortingOption[];
  label? : {
    text?: string;
    position?: HOME_LABEL_POSITION;
    isMath?: boolean;
  };
  flowSymbol?: FLOW_SYMBOL;
  targetLabels:{
    start: string;
    end: string;
  }
  homeConfig: IDgIntHomeConfig;
  styleConfig: IDgIntStyleConfig;
  diagramPadding?: {
    isEnabled?: boolean;
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  }
  config: {
    isUseImage: boolean;
    flowLayout: FLOW_LAYOUT;
    flowLayoutParam?: number;
    totalMaxWidth: number;
    homeMaxWidth: number;
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    draggableBgColor?: string;
    targetBgColor?: string;
    proportionalScoringMode?: SORTING_PROPORTIONAL_SCORING_MODE;
  };
  //
  ordering? : number[]; //deprecated use `homeConfig` instead
}

export function generateDefaultDgIntElementDnDSorting(): IDgIntElementDnDSorting {
  return {
    constructionType: DgIntConstructionType.DND_SORTING,
    targetLabels: {
      start: 'Lowest',
      end: 'Highest',
    },
    options: [
      { label : 'Triangle', isPreFilled: false },
      { label : 'Square'  , isPreFilled: false },
      { label : 'Pentagon', isPreFilled: true },
      { label : 'Hexagon' , isPreFilled: false},
    ],
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    styleConfig: {
      styleMode: DND_STYLE_MODE.DEFAULT,
    },
    config: {
      isUseImage: false,
      flowLayout: FLOW_LAYOUT.LEFT_TO_RIGHT,
      totalMaxWidth: 200,
      homeMaxWidth: 200,
      homePosition: LAYOUT_HOME_POSITION.TOP,
      padding: [0.5,0.75],
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
      proportionalScoringMode: SORTING_PROPORTIONAL_SCORING_MODE.POSITIONAL,
    }
  }
}

export function resolveDnDSortingOptions(element : IDgIntElementDnDSorting) : void {
  if (element.homeConfig == undefined) 
    element.homeConfig = {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    }
  if (element.extraOptions == undefined) element.extraOptions = [];
  if (element.label == undefined) element.label = {text: '', position: HOME_LABEL_POSITION.TOP_LEFT};
  if (element.diagramPadding == undefined) element.diagramPadding = {left: 0, right: 0, top: 0, bottom: 0};
  
  const extraOptionIds = element.extraOptions.map(o => o.id);
  
  const [idChangeMap, newOptionIds, newTargetIds] = regenerateIdOnDnDSorting(element.options, element.extraOptions);
  const allOptionIds = [...element.options.filter(o => !o.isPreFilled).map(o => o.id), ...extraOptionIds];
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  
  const homeCount = element.homeConfig.element.length;
  const targetIds = element.options.map(o => o.targetId);
  const optionIds = [...element.options.map(o => o.id), ...extraOptionIds];
  const homeIds = Array.from({length: homeCount}, (_, i) => unusedId(i));
  element.styleConfig = resolveDnDStyleConfig(
    element.styleConfig, idChangeMap, newOptionIds, newTargetIds,
    targetIds, optionIds, homeIds
  );
}

function regenerateIdOnDnDSorting(options: IDgIntElementDnDSortingOption[], extra: IDgIntElementDnDSortingOption[]):
  [Map<string,string>, string[], string[]]
{
  let changeMap = new Map<string,string>();
  let newOptionIds = [];
  let newTargetIds = [];
  const idGenerator = new IdGenerator();
  for (let option of options){
    let optionId = idGenerator.generate('option_');
    let targetId = idGenerator.generate('target_');
    if (option.id) changeMap.set(option.id, optionId); else newOptionIds.push(optionId);
    if (option.targetId) changeMap.set(option.targetId, targetId); else newTargetIds.push(targetId);
    option.id = optionId;
    option.targetId = targetId;
    delete option.isExtra;
  }
  for (let option of extra){
    let optionId = idGenerator.generate('option_');
    if (option.id) changeMap.set(option.id, optionId); else newOptionIds.push(optionId);
    option.id = optionId;
    option.isExtra = true;
  }
  return [changeMap, newOptionIds, newTargetIds];
}

export function renderDgIntDndSorting(
  element : IDgIntElementDnDSorting, svgElement : SVGSVGElement,
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks, isLabelWide: boolean = false
) {
  // backward compatibility
  if (element.styleConfig == undefined) element.styleConfig = {};
  // if ((element.options.length > 0 && element.options[0].id == undefined) || 
  //   (element.homeConfig == undefined )
  // ){
  element = cloneDeep(element) as IDgIntElementDnDSorting;
  resolveDnDSortingOptions(element);
  // }
  //
  
  const options = [...element.options, ...element.extraOptions];
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  const nonPreFilledIdToIndexMap = new Map(options.filter(o => !o.isPreFilled).map((option,i) => [option.id, i]));
  const activeOptions = options.filter(o => !o.isPreFilled && !o.isExtra);
  const answerSet: IDgIntElementDnDAnswerSet = activeOptions.map(o => ({
    targetId: o.targetId, 
    optionIds: [o.id], 
  }));
  const extraHomeWidth = isSortingDnDOptionsContainMathOption(options) ? 1 : 0;
  // const proportionalScoringMode = element.config.proportionalScoringMode 
  //   ?? SORTING_PROPORTIONAL_SCORING_MODE.POSITIONAL;
  const styleParam = getStyleParam(styleProfiles);
  
  // queue the image size calculation
  if (element.config.isUseImage) {
    for (let option of options){
      const url = option.image?.url;
      if (url) imageManager.queueSizeCalculation(url, option);
    }
  }
  
  const homeMaxWidthEm = element.config.homeMaxWidth * 20/100;
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const flowLayout = element.config.flowLayout;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const totalMaxWidthEm = element.config.totalMaxWidth * 20/100;
  const padding = element.config.padding ?? [0.5, 0.75];
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const [elementColors, borderColors] = generateElementColorsObject(element.styleConfig, element.homeConfig)
  
  const canvasStyle = getStyle(svgElement);
  const targetMaxWidth = calculateTargetMaxWidth(dg, totalMaxWidthEm, options.length, flowLayout);
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    const innerMaxWidth = targetMaxWidth - 2*padding[1];
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT, innerMaxWidth);
  }
  const multilineTextMaxAvailableWidth = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    const innerMaxWidth = totalMaxWidthEm;
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT, innerMaxWidth);
  }
  const mathStyleConfig = {svgFontSize: canvasStyle.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  
  let optionInnerDiagrams: DgDiagram[] = options.map((s) => { 
    let textDiagramGenerator = s.isMath ? mathExpression : multilineText;
    return generateImageTextDiagram(
      dg, imageManager, s.label, s.image,
      textDiagramGenerator, undefined, element.config.padding);
  });
  const optionsBB = optionInnerDiagrams.map(o => o.bounding_box());
  
  const choicesMaxwidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
  const choicesMaxheight = Math.max(...optionsBB.map(bb => bb[1].y - bb[0].y)) + 2*padding[0];
  const dndBlock = generateDnDBlock(dg, choicesMaxwidth, choicesMaxheight, styleProfiles);
  const optionDiagrams = optionInnerDiagrams.map((o,i) => {
    const id = options[i].id;
    const source = o.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    let bg     = dndBlock.position(source.origin).fill(draggableBgColor).append_tags(TAG_DND_DRAGGABLE);
    if (elementColors[id]) bg = bg.fill(elementColors[id]);
    if (element.styleConfig.isUseCustomBorderColors){
      bg = bg.stroke(element.styleConfig.commonDraggableBorderColor);
      if (borderColors[id]) bg = bg.stroke(borderColors[id]);
    }
    const combined = bg.combine(source);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  });
  
  const flowDiagram = generateFlowDiagram(
    dg, int, dndBlock, options, optionDiagrams, 
    element.targetLabels.start, element.targetLabels.end,
    element.config, element.flowSymbol, canvasStyle, styleParam,
  );
  let diagram = flowDiagram;
  diagram = generatePaddedDiagram(dg, diagram, element.diagramPadding);
  if (element.label?.text){
    const multilineFn = isLabelWide ? multilineTextMaxAvailableWidth : multilineText;
    const textDiagramGenerator = element.label.isMath ? mathExpression : multilineFn;
    const labelDiagram = textDiagramGenerator(element.label.text);
    diagram = attachLabelDiagram(dg, diagram, labelDiagram, element.label.position, EM_DND_ELEMENT_PADDING)
  }
  
  const groupedOptionDiagrams = groupByHome(optionDiagrams, element.homeConfig.element, idToIndexMap);
  const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, element.homeConfig,
    groupedOptionDiagrams, diagram, 
    homeMaxWidthEm, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, imageManager,
    extraHomeWidth);
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, element.homeConfig, styleParam, nonPreFilledIdToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(element.homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  
  const idToDiagramMap = new Map<string, DgDiagram>();
  {
    // setup diagram for dnd elements
    // dnd target
    // const targetBlocks = (flowDiagram.children[0].children as DgDiagram[])
    //   .filter(d => d.contain_tag(TAG_DND_BLOCK));
    const targetBlocks = flowDiagram.get_tagged_elements(TAG_DND_TARGET);
    for (let i = 0; i < activeOptions.length; i++) {
      const option = activeOptions[i];
      let targetBlock = targetBlocks[i].fill(targetBgColor);
      targetBlock = dg.style.applyStyleProfiles(targetBlock, styleProfiles);
      if (elementColors[option.targetId]) targetBlock = 
        targetBlock.fill(elementColors[option.targetId]);
      if (element.styleConfig.isUseCustomBorderColors){
        targetBlock = targetBlock.stroke(element.styleConfig.commonTargetBorderColor);
        if (borderColors[option.targetId]) targetBlock = targetBlock.stroke(borderColors[option.targetId]);
      }
      idToDiagramMap.set(option.targetId, targetBlock);
    }
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let option of options){
      let label: string;
      if (option.label) {
        label = option.label;
      } else if (option.image?.url) {
        label = getFilenameFromPath(option.image.url);
      } else {
        label = option.id;
      }
      idToLabelMap.set(option.id, label);
    }
  }
  
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.filter(o => !o.isPreFilled).map(o => o.id),
      targets: activeOptions.map(o => o.targetId),
      homes: homeIdList,
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram: dg.diagram_combine(diagram, ...homeDiagrams, labelDiagram, ...homeGroupBg),
      idToDiagramMap,
    },
    home: {
      config: element.homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    functions: {
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMapSortingDnD(element)
    },
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable: false,
      dndIsFilledMode: DND_ISFILLED_MODE.FILL_ALL_TARGET
    },
    scoring: {
      isAllowMultipleAnswers: false,
      answerSets: [answerSet],
      idToValueMap: new Map<string, any>([[KEY_OPTION_DATA_LIST, options]]),
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: 0,
      // activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: SortingDnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

function calculateTargetMaxWidth(dg: DgLib, totalMaxWidth: number, option_count: number, layout: FLOW_LAYOUT) : number {
  switch (layout) {
    case FLOW_LAYOUT.LEFT_TO_RIGHT:
    case FLOW_LAYOUT.RIGHT_TO_LEFT: {
      // totalMaxWidth = n*maxWidth + (n-1)*(2*padding + arrowWidth) 
      // maxWidth = (totalMaxWidth - (n-1)*(2*padding + arrowWidth)) / n
      const n = option_count;
      return (totalMaxWidth - (n-1)*(2*EM_DND_ELEMENT_PADDING + ARROW_BASE_LENGTH)) / n;
    }
    case FLOW_LAYOUT.TOP_TO_BOTTOM:
    case FLOW_LAYOUT.BOTTOM_TO_TOP: {
      return totalMaxWidth;
    }
  }
}

function getSymbolDiagram(dg: DgLib, symbol: FLOW_SYMBOL, layout: FLOW_LAYOUT): DgDiagram {
  switch (symbol) {
    case FLOW_SYMBOL.NONE: return dg.empty();
    case FLOW_SYMBOL.ARROW: 
    default: return generateArrow(dg, layout);
  }
}

function generateFlowDiagram(
  dg : DgLib,  int : DgInt,
  dndBlock: DgDiagram,
  allOptions: IDgIntElementDnDSortingOption[],
  optionDiagrams : DgDiagram[],
  startLabel: string, endLabel: string,
  config : IDgIntElementDnDSorting['config'],
  symbol: FLOW_SYMBOL,
  style : IDgIntStyle,
  styleParam: DgIntStyleParam,
) : DgDiagram {
  const layout = config.flowLayout;
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, style.pxFontSize, style.fontFamily, EM_LINE_HEIGHT);
  }
  const mathStyleConfig = {svgFontSize: style.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  const mathOrText = (s: string, isMath: boolean) => isMath ? mathExpression(s) : multilineText(s);
  const dndBlockWidth = dg.geometry.size(dndBlock)[0];
  const labelDgGenerator = (s: string, isMath: boolean) => {
    const labelDg = mathOrText(s, isMath);
    if (!styleParam.dnd_sort_outline_target_label) {
      return labelDg;
    } else {
      const size = dg.geometry.size(labelDg);
      const rect = dg.rectangle(dndBlockWidth, size[1])
      return dg.diagram_combine(rect, labelDg);
    }
  }
  
  const options = allOptions.filter(o => !o.isExtra);
  const symbolDg = getSymbolDiagram(dg, symbol, layout);
  let diagrams : DgDiagram[] = [];
  for (let i = 0; i < options.length; i++) {
    if (options[i].isPreFilled){
      diagrams.push(optionDiagrams[i].strokedasharray([]));
    } else {
      diagrams.push(dndBlock.copy().append_tags(TAG_DND_TARGET));
    }
    if (i < options.length - 1) diagrams.push(symbolDg.copy());
  }
  
  // target labels for vertical flows, applied before alignment
  if (layout === FLOW_LAYOUT.TOP_TO_BOTTOM || layout === FLOW_LAYOUT.BOTTOM_TO_TOP) {
    for (let i = 0; i < options.length; i++) {
      if (!options[i].targetLabel) continue;
      const index = 2*i
      const blockDiagram = diagrams[index];
      const labelDg = labelDgGenerator(options[i].targetLabel, options[i].isTargetLabelMath);
      if (layout === FLOW_LAYOUT.TOP_TO_BOTTOM) {
        diagrams[index] = dg.distribute_vertical_and_align([labelDg, blockDiagram]);
      } else if (layout === FLOW_LAYOUT.BOTTOM_TO_TOP) {
        diagrams[index] = dg.distribute_vertical_and_align([blockDiagram, labelDg.vflip(undefined)]);
      }
    }
  }
  
  let flowDiagram : DgDiagram;
  switch (layout) {
    case FLOW_LAYOUT.LEFT_TO_RIGHT: {
      const flow = dg.distribute_horizontal_and_align(diagrams, EM_DND_ELEMENT_PADDING);
      flowDiagram = flow;
    } break;
    case FLOW_LAYOUT.RIGHT_TO_LEFT: {
      const flow = dg.distribute_horizontal_and_align(diagrams, EM_DND_ELEMENT_PADDING);
      flowDiagram = flow.hflip(undefined);
    } break;
    case FLOW_LAYOUT.TOP_TO_BOTTOM: {
      const flow = dg.distribute_vertical_and_align(diagrams, EM_DND_ELEMENT_PADDING);
      flowDiagram = flow;
    } break;
    case FLOW_LAYOUT.BOTTOM_TO_TOP: {
      const flow = dg.distribute_vertical_and_align(diagrams, EM_DND_ELEMENT_PADDING);
      flowDiagram = flow.vflip(undefined);
    } break;
  }
  
  // target labels for horizontal flows, applied after alignment
  const targetLabelsDg: DgDiagram[] = []
  if (layout === FLOW_LAYOUT.LEFT_TO_RIGHT || layout === FLOW_LAYOUT.RIGHT_TO_LEFT) {
    for (let i = 0; i < options.length; i++) {
      if (!options[i].targetLabel) continue;
      const index = 2*i
      const blockDiagram = flowDiagram.children[index];
      let labelDg = labelDgGenerator(options[i].targetLabel, options[i].isTargetLabelMath)
        .move_origin('bottom-center').position(blockDiagram.get_anchor('top-center'));
      targetLabelsDg.push(labelDg);
    }
  }
  
  const resultDiagrams = [flowDiagram, ...targetLabelsDg];
  const startLabelDg = multilineText(startLabel);
  const endLabelDg = multilineText(endLabel);
  switch (layout) {
    case FLOW_LAYOUT.RIGHT_TO_LEFT:
    case FLOW_LAYOUT.LEFT_TO_RIGHT: {
      if (startLabel){
        const ref = options[0].targetLabel ? targetLabelsDg[0] : flowDiagram.children[0];
        resultDiagrams.push(
          startLabelDg.move_origin('bottom-center').position(ref.get_anchor('top-center')));
      }
      if (endLabel){
        const ref = options[options.length - 1].targetLabel ? 
          targetLabelsDg[targetLabelsDg.length - 1] : flowDiagram.children[flowDiagram.children.length - 1];
        resultDiagrams.push(
          endLabelDg.move_origin('bottom-center').position(ref.get_anchor('top-center')));
      }
    } break;
    case FLOW_LAYOUT.TOP_TO_BOTTOM: {
      if (startLabel){
        const startTarget : DgDiagram = flowDiagram.children[0];
        resultDiagrams.push(
          startLabelDg.move_origin('bottom-center').position(startTarget.get_anchor('top-center')));
      }
      if (endLabel){
        const endTarget : DgDiagram = flowDiagram.children[flowDiagram.children.length - 1];
        resultDiagrams.push(
          endLabelDg.move_origin('top-center').position(endTarget.get_anchor('bottom-center')));
      }
    } break;
    case FLOW_LAYOUT.BOTTOM_TO_TOP: {
      if (startLabel){
        const startTarget : DgDiagram = flowDiagram.children[0];
        resultDiagrams.push(
          startLabelDg.move_origin('top-center').position(startTarget.get_anchor('bottom-center')));
      }
      if (endLabel){
        const endTarget : DgDiagram = flowDiagram.children[flowDiagram.children.length - 1];
        resultDiagrams.push(
          endLabelDg.move_origin('bottom-center').position(endTarget.get_anchor('top-center')));
      }
    } break;
  }
  
  return dg.diagram_combine(...resultDiagrams);
  
  
}

function generateArrow(dg: DgLib, layout: FLOW_LAYOUT) : DgDiagram {
  switch (layout) {
    case FLOW_LAYOUT.RIGHT_TO_LEFT:
    case FLOW_LAYOUT.LEFT_TO_RIGHT: {
      // ->
      const base = dg.line(dg.V2(-ARROW_BASE_LENGTH,0), dg.V2(0,0))
      const head0 = dg.line(dg.V2(-ARROW_HEAD_LENGTH,0), dg.V2(0,0)).rotate(dg.to_radian(ARROW_HEAD_ANGLE))
      const head1 = dg.line(dg.V2(-ARROW_HEAD_LENGTH,0), dg.V2(0,0)).rotate(dg.to_radian(-ARROW_HEAD_ANGLE))
      
      const arrow = dg.diagram_combine(base, head0, head1)
        .strokewidth(ARROW_STROKE_WIDTH).strokelinecap('round')
      return arrow;
    } 
    case FLOW_LAYOUT.TOP_TO_BOTTOM:
    case FLOW_LAYOUT.BOTTOM_TO_TOP: {
      // v
      return generateArrow(dg, FLOW_LAYOUT.RIGHT_TO_LEFT).rotate(dg.to_radian(-90));
    }
  }
  
}

function isSortingDnDOptionsContainMathOption(options: IDgIntElementDnDSortingOption[]): boolean {
  for (let option of options) if (option.isMath) return true;
  return false;
}

function longestCommonSubsequence<T>(arr1: T[], arr2: T[]): number {
  let m = arr1.length;
  let n = arr2.length;

  let L = new Array(m+1);
  for (let i = 0; i <= m; i++){
    L[i] = new Array(n+1).fill(0);
  }

  for (let i = 0; i <= m; i++){
    for (let j = 0; j <= n; j++){
      if (i == 0 || j == 0)
        L[i][j] = 0;
      else if  (arr1[i-1] == arr2[j-1])
        L[i][j] = L[i-1][j-1] + 1;
      else
        L[i][j] = Math.max(L[i-1][j], L[i][j-1])
    }
  }

  return L[m][n];
};


class SortingDnDStateManager extends DnDStateManager {
  
  //TODO: implement longeset common subsequence for partial scoring
  
  optionDataList: IDgIntElementDnDSortingOption[];
  prefilledListLabel: string[];
  constructor(
    public int: DgInt,
    public config: IDgIntDnDConfig,
  ) {
    super(int, config);
    
    this.optionDataList = config.scoring.idToValueMap.get(KEY_OPTION_DATA_LIST) ?? [];
    this.prefilledListLabel = this.optionDataList.map(o => {
      return o.isPreFilled ? o.label : undefined;
    });
  }
  
  getFormattedResponse(): string {
    let targetIdToFirstContentIdMap = new Map<string, string>();
    const state = this.getAnswerState();
    for (let container of state){
      const targetId = container.targetId;
      const firstContentId = container.optionIds[0];
      targetIdToFirstContentIdMap.set(targetId, firstContentId);
    }
    
    let sequence = [];
    for (let i in this.optionDataList){
      const optionData = this.optionDataList[i];
      const preFilledLabel = this.prefilledListLabel[i];
      if (preFilledLabel){
        sequence.push(`(${preFilledLabel})`)
      }else{
        const targetId = optionData.targetId;
        const contentId = targetIdToFirstContentIdMap.get(targetId);
        const contentLabel = this.config.label.idToLabelMap.get(contentId);
        sequence.push(contentLabel ?? "(EMPTY)");
      }
    }
    
    return sequence.join(',');
  }
  
}

function generateVoiceoverDataMapSortingDnD(element: IDgIntElementDnDSorting): Map<string, DgIntVoiceoverData> {
  const map = new Map<string, DgIntVoiceoverData>();
  for (const options of [...element.options, ...element.extraOptions]) {
    const id = options.id;
    const voiceover = options.voiceover;
    if (voiceover && voiceover.url && voiceover.script) {
      const trigger = new Subject<boolean>();
      map.set(id, {url: voiceover.url, trigger});
    }
  }
  return map;
}