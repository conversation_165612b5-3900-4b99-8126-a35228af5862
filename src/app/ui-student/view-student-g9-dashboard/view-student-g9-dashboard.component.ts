import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { StudentG9DashboardService } from '../student-g9-dashboard.service';
import { AuthService, IUserInfo } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { LangService } from '../../core/lang.service';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import * as moment from 'moment-timezone';
import { RoutesService } from '../../api/routes.service';
import { StudentG9ConnectionService } from '../student-g9-connection.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { EStyleProfile, StyleprofileService } from '../../core/styleprofile.service';
import { AssessmentsService } from 'src/app/assessments.service';
import { Subscription } from 'rxjs';
import { DomainValidationService } from 'src/app/core/domain-validation.service';
import { LDBLoginStrategy, LockdownService } from '../lockdown.service';
export enum StudentDashboardView {
  MAIN = "main",
  RESOURCES = "resources",
  TESTS = "tests",
  GEN_INSTR = "gen_Instructions",
  MINDS_ON = "minds_on",
  REMINDER_TO_SKIP = "reminder_to_skip",
  EXERCISE_SELECTION = "exercise_selection",
  CHECKLIST = "checklist"
}
import { IAsmtCoverPage } from './model/types';
import { DEFAULT_COVER_PAGE_INFO } from './model/default';
import { sanitizeCoverPagePayload } from './model/util';

export {IAsmtCoverPage}

let schlClassGroupType = new Set();

export enum StudentLandingModalType {
  UpcomingAssessments = 'UpcomingAssessments',
  VerifyStudentinfo = 'VerifyStudentinfo',
  AssessmentTypeInfo = 'AssessmentTypeInfo',
}
export const SEEN_WELCOME = 'SEEN_G9_WELCOME';

@Component({
  selector: 'view-student-g9-dashboard',
  templateUrl: './view-student-g9-dashboard.component.html',
  styleUrls: ['./view-student-g9-dashboard.component.scss']
})
export class ViewStudentG9DashboardComponent implements OnInit,OnDestroy{
  scheduledSessions;
  pageModal: PageModalController;
  isAssistiveTech: boolean = false;
  lockSubscription: any;
  testSubmitted: boolean;
  testSubmittedSub: Subscription = null;
  loggedInUserSub: Subscription = null;
  constructor(
    private router: Router,
    public langService: LangService,
    private pageModalService: PageModalService,
    private auth: AuthService,
    public loginGuard: LoginGuardService,
    private studentG9Connection: StudentG9ConnectionService,
    private dash: StudentG9DashboardService,
    private routes: RoutesService,
    private styleProfile: StyleprofileService,
    public whiteLabelService: WhitelabelService,
    private assessmentsService: AssessmentsService,
    private domainValidation: DomainValidationService,
    public lockdown: LockdownService
  ) {
  }
  
  StudentLandingModalType = StudentLandingModalType;
  isLoaded = false;

  public testSessionSlug: string;
  public hasAttempted: boolean = false;
  isWelcoming = false;
  styleProfileSub;
  displayName = '';
  currentModalType: StudentLandingModalType;
  student:any
  assessmentType:string = null;
  noClassDetection:boolean = false
  SCH_CLASS_GROUP_TYPES: Set<any> = new Set();

  coverPageInfo:Partial<IAsmtCoverPage> = DEFAULT_COVER_PAGE_INFO()
  coverPageLang:string

  createSchlClassGroupTypes(): Set<any>   {
    this.SCH_CLASS_GROUP_TYPES = new Set([ ...schlClassGroupType]);
    return this.SCH_CLASS_GROUP_TYPES;
  }

  async ngOnInit(): Promise<void> {

    await this.assessmentsService.initData();
    schlClassGroupType = new Set(this.assessmentsService.getEnabledAssessmentDefs().map(def => def.assessment_slug));

    this.createSchlClassGroupTypes();
    console.log(this.SCH_CLASS_GROUP_TYPES);

    this.loggedInUserSub = this.auth.user().subscribe(async (userInfo: IUserInfo) => {
      if (userInfo) {

        //if ABED system then check if the student is walk-in. If they are, then navigate them to the waiting room. 
        if( this.whiteLabelService.getSiteFlag('IS_STU_WALKIN_ENABLED') ){
          const walkInCheck = await this.auth.apiGet(this.routes.ABED_MY_WALKN, userInfo.uid, {
            query: {
              isStudentWalkIn: 1,
              isABED: 1, // todo: why is this ABED specific?
              sch_class_group_id: userInfo.sch_class_group_id
            }
          });
          if(walkInCheck.isStudentWalkIn){
            this.dash.goToWaitingRoom();
            this.isLoaded = true;
            return;
          }
        }
        
        if(userInfo.sch_class_id) { // If class id is not present, dashboard will not load and LDB will not be initialized
          this.lockdown.getLDBEncrptedLaunchLink({
            strategy: LDBLoginStrategy.AUTH_TOKEN,
            authToken: userInfo.accessToken,
            passwordSeed: userInfo.sch_class_id
          })
        }

        //this.lockSubscription = this.studentG9Connection.sub().subscribe(this.setStudentSoftLock)
        this.student = {
          schClassGroupId: userInfo.sch_class_group_id,
          firstName: userInfo.first_name.toUpperCase(),
          lastName: userInfo.last_name.toUpperCase(),
          oenOrSasn: userInfo.studentOenOrSasn,
          isSasnLogin: userInfo.isSasnLogin,
        }
        this.assessmentType = userInfo.assessmentType;
        if(!this.getIsStudentVerified()){
          this.verifyStudentModalStart();
        }      
        this.determineStudentDashboard(userInfo) // todo: what is this really doing?
        this.displayName = [userInfo.first_name, userInfo.middle_name, userInfo.last_name].join(' ')
        this.testSubmittedSub = this.studentG9Connection.testSubmitted.subscribe(testSubmitted => {
          this.testSubmitted = testSubmitted
          if(testSubmitted){
            this.studentG9Connection.showAssessmentPopup(true,'Q');
            this.goToAssessment()
          } 
        })
        this.domainValidation.studentDomainCheck();
        await this.initCoverPage(userInfo.sch_class_id, userInfo.uid);
      }
    })
    if(!this.pageModal) {
      this.pageModal = this.pageModalService.defineNewPageModal();
    }
    window.onbeforeunload = () => this.studentG9Connection.disconnect();
  }

  pageRefresh(){
    window.location.reload()
  }

  ngOnDestroy(){
    if(this.lockSubscription){
      this.lockSubscription.unsubscribe()
    }

    if(this.testSubmittedSub) {
      this.testSubmittedSub.unsubscribe();
    }

    if(this.loggedInUserSub) {
      this.loggedInUserSub.unsubscribe();
    }

    if(this.styleProfileSub) {
      this.styleProfileSub.unsubscribe()
    }
  }

  async determineStudentDashboard(userInfo: IUserInfo){
    // todo: a few different things happening in here, lots of hardcodings, what do they mean?

    if(!userInfo || !this.SCH_CLASS_GROUP_TYPES.has(userInfo.sch_class_group_type)) {
      return;
    }

    if(userInfo.assistive_tech) {
      this.isAssistiveTech = true;
    }
    if (userInfo.sch_class_group_type) {
      this.determineStudentFilter();
      this.isLoaded = true;
    }
    else {
      // todo: it is probably exiting here all of the time now... to clarify what we are losing from not seeing the other element
      const res = await this.auth.apiGet(this.routes.STUDENT_ASMT_FILTER, userInfo.uid, userInfo.uid);
      if(res["eqao_sdc.IS_G10"] != "1" && res["eqao_sdc.IS_G9"] != "1") {
        return;
      }
      this.determineStudentFilter()
      this.isLoaded = true;
    }

    if(localStorage.getItem(SEEN_WELCOME) != 'true') {
      this.isWelcoming = true;
    }
    this.dash.init(); //initialize g9 connection
  }

  determineStudentFilter() {
    if(!this.styleProfileSub) {
      //wait for default style profile to load before setting an override
      this.styleProfileSub = this.styleProfile.styleProfileSub.subscribe()
    }
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }

  isResourceAvailable(){
    return !!this?.coverPageInfo?.resource_td_id
  }

  goToResources() {
    this.dash.goToCoverPageResource(this.coverPageLang, this.coverPageInfo.resource_item_set_id, this.coverPageInfo.resource_td_id, { isFromStudentDashboard: true})
  }

  setStudentSoftLock = (res) => {

    if (res.isReady) {

      console.log(res, 'send soft lock')
      // setTimeout(function(){ alert("Hello"); }, 3000);
      if (this.isAssistiveTech) {
        this.studentG9Connection
          .updateStudentPosition({
            stageIndex: null,
            questionCaption: null,
            softLock: 1
          });
        this.studentG9Connection
          .updateStudentPosition({
            stageIndex: null,
            questionCaption: null,
            softLock: 1
          });
      }

    }
  }

  configureQueryParams() {
    if (this.auth.u()) {
      return {
        query: {
          schl_class_group_id: this.auth.u().sch_class_group_id,
          payCheck: true
        }
      }
    }
    return null;
  }

  studentDidNotPayModal: boolean = false;
  closeNotification(){
    this.studentDidNotPayModal = false;
  }

  async goToAssessment() {
    try {
      if(this.lockdown.isInsecure) {
        return this.loginGuard.quickPopup('txt_ldb_warning_popup')
      }

      const assessmentsInfo = await this.dash.loadAssessments();
      const sessions = assessmentsInfo[0].classes_sessions;
      if (sessions && sessions.length) {
        let session;
  
        if(this.testSessionSlug == null) {
          session = sessions[0];
        } 
        else {
          session = sessions.find(s => s.slug.toLowerCase().endsWith(this.testSessionSlug.toLowerCase())) || sessions[0];
        }
  
        if (!session) {
          this.loginGuard.disabledPopup(this.getStudentNoAssessmentSlug());
          return;
        }
        if(!session.isPaid) {
          this.studentDidNotPayModal = true;
          return;
        }
  
        this.router.navigate(
          [`${this.langService.c()}/student/assessment/${session.test_session_id}`]
        );
        
      }
      else {
        const scheduled_sessions = assessmentsInfo[0].scheduled_sessions;
        if (scheduled_sessions && scheduled_sessions.length > 0) {
          this.scheduledSessions = scheduled_sessions
          console.log(this.scheduledSessions)
          this.newScheduledModalStart();
        }
        else {
          this.loginGuard.disabledPopup(this.getStudentNoAssessmentSlug());
        }
      }
    }
    catch(e) {
      this.loginGuard.disabledPopup(e.message);
    }
  }

  newScheduledModalStart() {
    this.currentModalType = StudentLandingModalType.UpcomingAssessments;
    if(!this.pageModal) {
      this.pageModal = this.pageModalService.defineNewPageModal();
    }
    const config: any = {};
    this.pageModal.newModal({
      type: '',
      config,
      finish: config => this.newScheduledModalFinish()
    });
  }
  newScheduledModalFinish() {
    this.currentModalType = null;
    this.pageModal.closeModal();
  }

  verifyStudentModalStart(){
    if (this.whiteLabelService.getSiteFlag('IS_STU_VERIFY_IDENTITY')){
      this.currentModalType = StudentLandingModalType.VerifyStudentinfo;
      if(!this.pageModal) {
        this.pageModal = this.pageModalService.defineNewPageModal();
      }
      const config: any = {};
      this.pageModal.newModal({
        type: '',
        config,
        finish: config => this.verifyStudentModalFinish()
      });
    }
  }
  verifyStudentModalFinish(){
    this.currentModalType = null;
    this.pageModal.closeModal();
  }
  getIsStudentVerified(){
    const isStudentVerified = this.auth.getCookie("studentLastNameInput");
    if(isStudentVerified) return true;
    return false;
  }

  renderDateTime(dateTime) {
    return `${this.renderDay(dateTime)} at ${this.renderTime(dateTime)}`
  }

  renderDay(dateTime) {
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format(this.langService.tra('datefmt_day_month'));
  }

  renderTime(dateTime) {
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format('h:mm A')
  }

  logout(): void {
    if(this.lockdown.isRespondus) {
      return this.lockdown.ldbExitPrompt('ldb_exit_caption');
    }

    this.auth
      .logout()
      .then(r => {
        if (this.whiteLabelService.isBCED()){
          this.router.navigate(['/en/bced-landing/admin']);
        }
        else{
          this.router.navigate(['/en/login-router-st']);
        }
        setTimeout(() => {
          window.location.reload();
        }, 300);
      });
  }

  processWelcome() {
    this.dash.init(); //connects to the websockets, allows pop-ups to appear - register student as online.
    this.isWelcoming = false;
    localStorage.setItem(SEEN_WELCOME, 'true');
  }

  assessmentTypeInfoModalStart() {
    this.currentModalType = StudentLandingModalType.AssessmentTypeInfo;
    if(!this.pageModal) {
      this.pageModal = this.pageModalService.defineNewPageModal();
    }
    const config: any = {};
    this.pageModal.newModal({
      type: '',
      config,
      finish: config => this.assessmentTypeInfoModalFinish()
    });
  }

  assessmentTypeInfoModalFinish() {
    this.currentModalType = null
    this.pageModal.closeModal();
  }

  getLang() {
    return this.langService.getCurrentLanguage();
  }

  isLangActive(langCode: string) {
    return (this.getLang() === langCode);
  }

  setLang(langCode: string) {
    this.langService.setCurrentLanguage(langCode);
  }

  async initCoverPage(school_class_id:number, uid:number){
    let coverPage:Partial<IAsmtCoverPage>
    if (school_class_id){
      coverPage = await this.auth.apiGet(this.routes.TEST_COVER_PAGE_INFO, 1, {
        query: {
          school_class_id,
          uid
        }
      });
    }
    else {
      coverPage = {};
    }
    let isCoverPage = !!coverPage.type_slug
    if (!isCoverPage){
      this.noClassDetection = true;
      this.coverPageLang = this.coverPageInfo.lang
    }
    else {
      const {coverPageInfo, coverPageLang} = sanitizeCoverPagePayload(coverPage)
      this.coverPageInfo = coverPageInfo
      this.setLang(coverPageLang);
      this.coverPageLang = this.getLang()
      await this.lockdown.initSecurityConfiguration(coverPage.test_session_id, school_class_id)
    }
  }

  /**
   * Returns the appropriate slug for "no assessment" message based on whitelabel context
   * For ABED context, uses getSiteText to get context-specific text, otherwise uses txt_student_no_asmt
   */
  getStudentNoAssessmentSlug(): string {
    if (this.whiteLabelService.isABED()) {
      // Use getSiteText to get ABED-specific text, fallback to default slug
      const abedSlug = this.whiteLabelService.getSiteText('student_no_asmt_text');
      return abedSlug || 'txt_student_no_asmt';
    }
    return 'txt_student_no_asmt';
  }
}
